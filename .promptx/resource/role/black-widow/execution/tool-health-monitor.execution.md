<execution>
  <constraint>
    ## 工具健康监控技术约束
    - **监控覆盖率要求**：142个可用工具100%监控覆盖，无遗漏
    - **检查频率限制**：高频工具每5分钟检查，中频工具每15分钟，低频工具每小时
    - **响应时间约束**：健康检查单次耗时<5秒，批量检查<30秒
    - **状态存储限制**：工具状态信息本地缓存，避免重复检查
    - **降级策略约束**：工具失败后自动降级，3次失败标记为不可用
    - **恢复检测限制**：不可用工具每30分钟尝试恢复检测
    - **性能评分约束**：基于响应时间、成功率、结果质量综合评分
    - **备选切换限制**：主工具失败后5秒内完成备选工具切换
  </constraint>

  <rule>
    ## 工具健康监控强制规则
    - **预检查强制**：任何工具调用前必须进行健康检查
    - **状态更新强制**：工具调用后必须更新状态信息和性能数据
    - **降级触发强制**：连续3次失败自动触发降级，切换备选工具
    - **恢复检测强制**：不可用工具定期恢复检测，状态自动更新
    - **性能评分强制**：每次调用后更新工具性能评分
    - **备选准备强制**：每个主工具必须准备2-3个备选工具
    - **异常记录强制**：所有异常情况必须记录，用于分析优化
    - **监控报告强制**：定期生成工具健康监控报告
  </rule>

  <guideline>
    ## 工具健康监控指导原则
    - **预防优于治疗**：主动监控预防问题，而非被动响应
    - **智能降级**：根据工具重要性和可用性智能降级
    - **无缝切换**：备选工具切换对用户透明，不影响体验
    - **持续优化**：基于监控数据持续优化工具选择策略
    - **资源节约**：避免过度监控，合理分配监控资源
    - **用户友好**：监控过程不影响正常使用体验
    - **数据驱动**：基于真实监控数据做决策，避免主观判断
    - **系统稳定**：确保监控系统本身的稳定性和可靠性
  </guideline>

  <process>
    ## 工具健康监控执行流程

    ### 🔍 阶段1：工具状态初始化 (启动时)
    ```mermaid
    flowchart TD
        A[系统启动] --> B[加载工具清单]
        B --> C[读取历史状态]
        C --> D[批量健康检查]
        D --> E[状态分类]
        E --> F[性能评级]
        F --> G[备选工具准备]
        
        E --> E1[可用工具]
        E --> E2[不可用工具]
        E --> E3[未知状态工具]
        
        F --> F1[A级工具列表]
        F --> F2[B级工具列表]
        F --> F3[C级工具列表]
    ```

    **初始化步骤**：
    1. **工具清单加载**：从MCP索引加载142个可用工具清单
    2. **历史状态读取**：读取上次保存的工具状态和性能数据
    3. **批量健康检查**：对所有工具进行初始健康检查
    4. **状态分类**：将工具分为可用、不可用、未知状态三类
    5. **性能评级**：基于历史数据和当前检查结果进行性能评级
    6. **备选准备**：为每个主工具准备对应的备选工具列表

    ### ⚡ 阶段2：实时健康监控 (运行时)
    ```mermaid
    flowchart TD
        A[工具调用请求] --> B[预检查]
        B --> C{工具状态}
        C -->|可用| D[直接调用]
        C -->|不可用| E[备选切换]
        C -->|未知| F[快速检查]
        
        D --> G[调用监控]
        E --> G
        F --> G
        
        G --> H[结果评估]
        H --> I[状态更新]
        I --> J[性能评分更新]
        J --> K[异常记录]
    ```

    **实时监控机制**：
    ```json
    {
      "tool_status": {
        "tool_id": "firecrawl_search",
        "status": "available",
        "last_check": "2025-08-01T03:15:00Z",
        "response_time": 1.2,
        "success_rate": 0.95,
        "quality_score": 0.88,
        "failure_count": 0,
        "performance_grade": "A"
      },
      "backup_tools": [
        "tavily_search",
        "web_search_exa",
        "brave_web_search"
      ],
      "monitoring_config": {
        "check_interval": 300,
        "timeout_threshold": 30,
        "failure_threshold": 3,
        "recovery_interval": 1800
      }
    }
    ```

    ### 🔧 阶段3：异常处理与降级 (异常时)
    ```mermaid
    flowchart TD
        A[工具调用失败] --> B[失败计数+1]
        B --> C{失败次数}
        C -->|<3次| D[重试机制]
        C -->|≥3次| E[标记不可用]
        
        D --> F[延迟重试]
        F --> G[重试调用]
        G --> H{重试结果}
        H -->|成功| I[重置计数]
        H -->|失败| B
        
        E --> J[备选工具选择]
        J --> K[无缝切换]
        K --> L[用户通知]
        L --> M[异常记录]
    ```

    **降级策略**：
    1. **渐进式重试**：首次失败立即重试，第二次失败延迟5秒重试，第三次失败延迟15秒重试
    2. **智能降级**：3次失败后自动标记为不可用，切换到备选工具
    3. **备选选择**：按性能评级选择最佳备选工具，确保服务质量
    4. **用户通知**：透明告知用户工具切换情况，但不影响使用体验
    5. **异常记录**：详细记录异常信息，用于后续分析和优化

    ### 🔄 阶段4：恢复检测与优化 (定期)
    ```mermaid
    flowchart TD
        A[定期检查触发] --> B[不可用工具列表]
        B --> C[恢复检测]
        C --> D{检测结果}
        D -->|恢复| E[状态更新为可用]
        D -->|仍不可用| F[保持不可用状态]
        
        E --> G[性能重新评估]
        F --> H[下次检查安排]
        
        G --> I[工具评级更新]
        I --> J[备选策略调整]
        J --> K[监控报告生成]
    ```

    **恢复检测机制**：
    - **检测频率**：不可用工具每30分钟进行一次恢复检测
    - **检测方法**：使用轻量级测试请求验证工具可用性
    - **恢复条件**：连续2次检测成功才标记为恢复可用
    - **性能重评**：恢复后重新评估工具性能，更新评级
    - **策略调整**：基于恢复情况调整备选工具策略

    ## 工具性能评分算法

    ### 综合性能评分公式
    ```python
    def calculate_performance_score(tool_metrics):
        response_time_score = 1.0 - min(tool_metrics.avg_response_time / 30.0, 1.0)
        success_rate_score = tool_metrics.success_rate
        quality_score = tool_metrics.avg_quality_score
        availability_score = tool_metrics.uptime_percentage
        
        performance_score = (
            response_time_score * 0.25 +
            success_rate_score * 0.35 +
            quality_score * 0.25 +
            availability_score * 0.15
        )
        
        return min(max(performance_score, 0.0), 1.0)
    ```

    ### 性能评级标准
    ```json
    {
      "performance_grades": {
        "A": {
          "score_range": [0.85, 1.0],
          "description": "优秀工具，优先使用",
          "characteristics": ["响应快", "成功率高", "质量好", "稳定性强"]
        },
        "B": {
          "score_range": [0.70, 0.84],
          "description": "良好工具，备选使用",
          "characteristics": ["性能良好", "偶有问题", "基本可靠"]
        },
        "C": {
          "score_range": [0.50, 0.69],
          "description": "一般工具，应急使用",
          "characteristics": ["性能一般", "问题较多", "不够稳定"]
        },
        "D": {
          "score_range": [0.0, 0.49],
          "description": "问题工具，避免使用",
          "characteristics": ["性能差", "经常失败", "不可靠"]
        }
      }
    }
    ```

    ## 监控数据存储结构

    ### 工具状态数据库
    ```json
    {
      "tool_health_db": {
        "tools": {
          "firecrawl_search": {
            "basic_info": {
              "name": "firecrawl_search",
              "category": "搜索类",
              "priority": "A级",
              "description": "强大的网页搜索工具"
            },
            "current_status": {
              "status": "available",
              "last_check": "2025-08-01T03:15:00Z",
              "consecutive_failures": 0,
              "last_failure": null
            },
            "performance_metrics": {
              "avg_response_time": 1.2,
              "success_rate": 0.95,
              "avg_quality_score": 0.88,
              "uptime_percentage": 0.98,
              "total_calls": 1250,
              "successful_calls": 1188
            },
            "backup_tools": [
              "tavily_search",
              "web_search_exa",
              "brave_web_search"
            ],
            "monitoring_config": {
              "check_interval": 300,
              "timeout_threshold": 30,
              "failure_threshold": 3,
              "recovery_interval": 1800
            }
          }
        },
        "monitoring_summary": {
          "total_tools": 142,
          "available_tools": 135,
          "unavailable_tools": 7,
          "a_grade_tools": 45,
          "b_grade_tools": 62,
          "c_grade_tools": 28,
          "d_grade_tools": 7,
          "last_update": "2025-08-01T03:15:00Z"
        }
      }
    }
    ```

    ### 异常记录日志
    ```json
    {
      "exception_log": [
        {
          "timestamp": "2025-08-01T03:10:00Z",
          "tool_id": "web_search_exa",
          "exception_type": "timeout",
          "error_message": "Request timeout after 30 seconds",
          "context": {
            "query": "AI技术发展趋势",
            "attempt_number": 2,
            "backup_used": "tavily_search"
          },
          "resolution": "switched_to_backup",
          "impact": "minimal"
        }
      ]
    }
    ```
  </process>

  <criteria>
    ## 工具健康监控质量标准

    ### 监控覆盖率指标
    - ✅ 工具监控覆盖率 = 100%
    - ✅ 状态检查准确率 > 95%
    - ✅ 异常检测及时率 > 98%
    - ✅ 恢复检测成功率 > 90%

    ### 响应性能指标
    - ✅ 健康检查响应时间 < 5秒
    - ✅ 批量检查完成时间 < 30秒
    - ✅ 备选切换时间 < 5秒
    - ✅ 状态更新延迟 < 1秒

    ### 降级恢复指标
    - ✅ 自动降级成功率 > 95%
    - ✅ 备选切换成功率 > 98%
    - ✅ 恢复检测准确率 > 90%
    - ✅ 异常处理完整率 = 100%

    ### 系统稳定性指标
    - ✅ 监控系统可用性 > 99%
    - ✅ 数据存储完整率 = 100%
    - ✅ 性能评分准确率 > 85%
    - ✅ 用户体验影响 < 5%
  </criteria>
</execution>
