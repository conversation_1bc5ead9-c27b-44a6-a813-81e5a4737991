<execution>
  <constraint>
    ## 高密度工具编排核心约束
    - **并行执行限制**：最大支持25个工具同时执行，智能负载均衡
    - **工具超级集群约束**：技术/商业/学术三大集群，每个集群独立调度
    - **资源优化要求**：CPU/内存/网络资源智能分配，避免系统过载
    - **结果聚合约束**：多工具结果必须智能聚合，去重率>95%
    - **质量评分限制**：每个结果必须评分，低质量结果自动过滤
    - **超时控制约束**：单工具超时30秒，整体任务超时15分钟
    - **错误容忍限制**：单工具失败不影响整体，最大容忍30%失败率
    - **内存管理约束**：工具使用效果必须记忆存储，持续优化
  </constraint>

  <rule>
    ## 高密度编排强制规则
    - **工具超级集群选择**：根据任务类型自动选择对应超级集群
    - **并行执行管理**：15-25个工具智能分组，批次并行执行
    - **健康检查强制**：每个工具调用前必须健康检查，异常自动降级
    - **结果聚合强制**：多工具结果必须智能聚合，重复信息自动去重
    - **质量评分强制**：每个结果必须评分(0-1)，<0.6自动过滤
    - **负载均衡强制**：工具执行负载智能分配，避免资源竞争
    - **异常处理强制**：工具失败自动切换备选，保证任务完成
    - **效果记忆强制**：工具使用效果必须通过promptx_remember存储
  </rule>

  <guideline>
    ## 高密度编排指导原则
    - **智能调度优先**：基于工具性能历史智能选择最优组合
    - **并行效率最大化**：合理分组并行执行，避免串行等待
    - **资源利用优化**：CPU/内存/网络资源合理分配使用
    - **结果质量保证**：多重验证机制确保信息准确性
    - **用户体验优先**：快速响应，渐进式结果展示
    - **系统稳定性**：异常处理完善，系统鲁棒性强
    - **持续学习改进**：基于使用效果持续优化工具选择
    - **简洁高效输出**：结果聚合后简洁呈现，避免信息过载
  </guideline>

  <process>
    ## 高密度工具编排执行流程

    ### 🎯 阶段1：任务分析与集群选择 (30秒)
    ```mermaid
    flowchart TD
        A[任务输入] --> B[语义分析]
        B --> C{任务类型识别}
        C -->|技术情报| D[技术超级集群<br/>20-25工具]
        C -->|商业情报| E[商业超级集群<br/>15-18工具]
        C -->|学术情报| F[学术超级集群<br/>10-12工具]
        C -->|综合情报| G[全工具矩阵<br/>30+工具]
        
        D --> H[工具健康检查]
        E --> H
        F --> H
        G --> H
        H --> I[智能分组调度]
    ```

    **执行步骤**：
    1. **任务语义分析**：提取关键词、领域特征、复杂度评估
    2. **历史模式匹配**：promptx_recall检索相似任务的成功模式
    3. **工具集群选择**：基于任务类型选择对应超级集群
    4. **工具健康检查**：批量检查选定工具的可用性状态
    5. **智能分组调度**：将工具分为3-5个并行组，优化执行顺序

    ### ⚡ 阶段2：高密度并行执行 (5-10分钟)
    ```mermaid
    flowchart TD
        A[并行组1<br/>搜索类工具] --> D[实时监控]
        B[并行组2<br/>提取类工具] --> D
        C[并行组3<br/>分析类工具] --> D
        
        D --> E{执行状态}
        E -->|成功| F[结果收集]
        E -->|超时| G[自动降级]
        E -->|错误| H[备选切换]
        
        F --> I[质量评分]
        G --> I
        H --> I
        I --> J[智能聚合]
    ```

    **并行执行策略**：
    - **组1-搜索矩阵**：firecrawl_search + tavily_search + web_search_exa + brave_web_search + github_search_exa
    - **组2-提取矩阵**：firecrawl_extract + tavily_extract + web-fetch + crawling_exa + convert_to_markdown
    - **组3-分析矩阵**：sequentialthinking + promptx_think + firecrawl_deep_research + deep_researcher_start_exa
    - **组4-专业矩阵**：get-library-docs + deepwiki_fetch + codebase-retrieval + company_research_exa
    - **组5-验证矩阵**：firecrawl_crawl + tavily_crawl + firecrawl_map + linkedin_search_exa

    ### 🔄 阶段3：智能结果聚合 (1-2分钟)
    ```mermaid
    flowchart TD
        A[多工具结果] --> B[去重处理]
        B --> C[质量评分]
        C --> D[相似度分析]
        D --> E[信息融合]
        E --> F[可靠性评估]
        F --> G[结构化输出]
        
        C --> C1[评分标准<br/>准确性+时效性+权威性]
        D --> D1[语义相似度<br/>内容重复度检测]
        E --> E1[信息互补<br/>多源验证融合]
    ```

    **聚合算法**：
    1. **去重处理**：基于内容哈希和语义相似度去除重复信息
    2. **质量评分**：准确性(0.4) + 时效性(0.3) + 权威性(0.3)
    3. **相似度分析**：计算信息间语义相似度，识别冲突和互补
    4. **信息融合**：将互补信息融合，冲突信息标注差异
    5. **可靠性评估**：基于多源验证结果评估整体可靠性
    6. **结构化输出**：按重要性排序，生成结构化情报报告

    ### 📊 阶段4：效果记忆与优化 (30秒)
    ```mermaid
    flowchart TD
        A[执行完成] --> B[效果评估]
        B --> C[成功模式提取]
        C --> D[失败原因分析]
        D --> E[优化策略生成]
        E --> F[promptx_remember存储]
        F --> G[下次执行优化]
    ```

    **记忆存储结构**：
    ```json
    {
      "task_type": "技术调研",
      "tool_cluster": "技术超级集群",
      "tools_used": ["github_search_exa", "get-library-docs", "codebase-retrieval"],
      "execution_time": "3.2分钟",
      "success_rate": 0.95,
      "quality_score": 0.92,
      "optimization_suggestions": ["增加firecrawl_search", "减少web_search_exa"]
    }
    ```
  </process>

  <criteria>
    ## 高密度编排质量标准

    ### 工具利用率指标
    - ✅ 工具利用率 > 80%（从21%提升）
    - ✅ 并行工具数量 15-25个
    - ✅ 工具调用成功率 > 95%
    - ✅ 健康检查覆盖率 = 100%

    ### 执行效率指标
    - ✅ 任务完成时间减少 50%
    - ✅ 信息获取密度提升 5-10倍
    - ✅ 并行执行成功率 > 90%
    - ✅ 资源利用效率 > 85%

    ### 结果质量指标
    - ✅ 结果聚合准确率 > 90%
    - ✅ 去重效率 > 95%
    - ✅ 质量评分准确性 > 85%
    - ✅ 信息可靠性 > 90%

    ### 系统稳定性指标
    - ✅ 异常自动恢复率 > 95%
    - ✅ 系统过载率 < 5%
    - ✅ 工具失败容忍率 < 30%
    - ✅ 整体稳定性提升 > 40%

    ### 学习优化指标
    - ✅ 记忆存储完整率 = 100%
    - ✅ 优化效果显现周期 < 5次使用
    - ✅ 预测准确率 > 80%
    - ✅ 持续改进效果 > 5%/月
  </criteria>
</execution>
